# 预付费认款功能实现说明

## 功能概述

在银行流水认款页面中新增了"预付费认款"Tab，用于处理预付费订单的认款操作。该功能严格遵循苹果设计规范和交互标准。

## 实现的功能特性

### 1. 预付费认款Tab
- 在银行流水认款详情页面新增"预付费认款"Tab
- 支持分页显示预付费订单列表
- 支持按订单编号筛选功能
- 提供复选框多选功能
- 第一列提供可输入的金额输入框（InputNumber组件）

### 2. 数据展示
- **订单编号**: 显示预付费订单的编号
- **合成编号**: 显示订单的合成编号（total_num）
- **收入类型**: 显示订单的收入类型
- **分账序号**: 显示分账序号
- **计费状态**: 显示当前的计费状态

### 3. 认款操作
- 支持多选订单进行批量认款
- 每行提供金额输入框，用户可手动输入认款金额
- 只有选中的行才能输入认款金额
- 提供认款确认对话框，显示认款金额和选中项目数量
- 认款成功后自动刷新银行流水详情和列表数据

### 4. 数据验证
- 认款金额不能超过银行流水总金额
- 未选中任何项目时禁用认款按钮
- 认款金额超出限制时显示警告提示

## 技术实现细节

### API接口
1. `getPrepaidStatements(bankStatementId, params)` - 获取预付费认款列表
   - 接口地址: `/bank-statements/<bank_statement_id>/prepaid`
   - 支持参数: `page`, `pageSize`, `order_num`

2. `submitPrepaidRecognition(bankStatementId, data)` - 提交预付费认款
   - 接口地址: `/bank-statements/<bank_statement_id>/prepaid/recognition`
   - 请求数据格式:
   ```json
   {
     "prepaid": [
       {
         "amount": "0",
         "sub_order_no": "string"
       }
     ]
   }
   ```

### 类型定义
```typescript
// 预付费认款记录
interface PrepaidStatementItem {
  order_num: string;
  total_num: string;
  income_type: string;
  account_seq: string;
  bill_status: string;
}

// 预付费认款请求项
interface PrepaidRecognitionRequestItem {
  amount: string;
  sub_order_no: string;
}

// 预付费认款请求
interface PrepaidRecognitionRequest {
  prepaid: PrepaidRecognitionRequestItem[];
}
```

### 组件结构
- **PrepaidTab.vue**: 独立的预付费认款Tab组件
- **RecognitionDetail.vue**: 主认款详情页面，集成了预付费认款Tab

### 核心功能实现
1. **数据加载**: 支持分页和筛选的预付费订单列表
2. **金额输入**: 使用PrimeVue的InputNumber组件，支持货币格式
3. **多选操作**: 使用DataTable的多选功能
4. **认款提交**: 构建符合API要求的请求数据格式
5. **数据刷新**: 认款成功后自动刷新相关数据

## 用户交互流程

1. 用户进入银行流水认款详情页面
2. 点击"预付费认款"Tab
3. 系统加载预付费订单列表
4. 用户可以通过订单编号筛选数据
5. 用户选择需要认款的订单（复选框）
6. 在选中行的金额输入框中输入认款金额
7. 点击认款按钮
8. 系统显示确认对话框
9. 用户确认后提交认款
10. 系统显示成功提示并刷新数据

## 设计规范遵循

### 苹果设计规范
- 使用清晰的视觉层次和间距
- 采用一致的颜色方案（绿色主题）
- 提供清晰的用户反馈和状态提示
- 使用标准的交互模式（复选框、按钮、对话框）

### 交互标准
- 禁用状态的明确视觉反馈
- 加载状态的进度指示
- 错误处理和用户提示
- 确认操作的二次确认机制

## 文件结构

```
src/
├── views/receipt/
│   ├── PrepaidTab.vue          # 预付费认款Tab组件
│   └── RecognitionDetail.vue   # 主认款详情页面
├── services/
│   └── bankStatement.ts        # 银行流水相关API服务
└── types/
    └── bankStatement.ts        # 银行流水相关类型定义
```

## 金额统计交互

### 问题解决
修复了预付费认款页面与银行流水详情中的金额统计没有交互的问题：

1. **问题原因**: 预付费认款Tab是独立组件，其金额计算与主页面的统计数据分离
2. **解决方案**: 通过事件通信机制，让预付费Tab实时向父组件报告当前选中的认款金额
3. **实现细节**:
   - 预付费Tab通过`updatePrepaidAmount`事件向父组件发送金额变化
   - 主页面监听该事件并更新`prepaidRecognitionAmount`状态
   - 金额计算逻辑包含预付费认款金额，确保统计数据准确

### 交互效果
- **当前认款金额**: 包含后付费认款 + 预付费认款 + 银行确认中金额
- **剩余金额**: 银行流水总金额 - 当前认款金额
- **认款金额占比**: 实时反映所有认款操作的进度
- **实时更新**: 预付费认款金额变化时，左侧统计面板立即更新

## 注意事项

1. **数据同步**: 预付费认款成功后会触发父组件的数据刷新，确保银行流水详情和其他Tab的数据保持同步
2. **金额统计**: 预付费认款金额实时影响左侧面板的统计数据，确保数据一致性
3. **错误处理**: 所有API调用都包含完整的错误处理和用户提示
4. **性能优化**: 使用懒加载和分页来处理大量数据
5. **响应式设计**: 组件支持不同屏幕尺寸的响应式布局

## 后续扩展

该实现为预付费认款功能提供了完整的基础架构，可以根据业务需求进行以下扩展：
- 添加更多筛选条件
- 支持批量导入认款数据
- 添加认款历史记录查看
- 集成更复杂的金额计算逻辑
