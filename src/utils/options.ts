import { type Ref } from "vue";
import { getStaticDataList } from "../services/public";

/**
 * 通用的静态数据选项加载器
 * @param word - 静态数据的关键字
 * @param optionsRef - 存储选项的响应式引用
 * @param errorMessage - 错误提示信息
 * @param onError - 错误处理回调函数
 */
export const loadStaticOptions = async (
  word: string,
  optionsRef: Ref<{ label: string; value: string }[]>,
  errorMessage: string,
  onError?: (error: any, message: string) => void
) => {
  try {
    const response = await getStaticDataList({ word });
    optionsRef.value = response.data.map((item) => ({
      label: item.data_value,
      value: item.data_value,
    }));
  } catch (error) {
    if (onError) {
      onError(error, errorMessage);
    } else {
      console.error(errorMessage, error);
    }
  }
};

/**
 * 创建静态选项加载器的工厂函数
 * @param word - 静态数据的关键字
 * @param errorMessage - 错误提示信息
 * @returns 返回一个加载函数
 */
export const createStaticOptionsLoader = (word: string, errorMessage: string) => {
  return (
    optionsRef: Ref<{ label: string; value: string }[]>,
    onError?: (error: any, message: string) => void
  ) => loadStaticOptions(word, optionsRef, errorMessage, onError);
};

/**
 * 预定义的常用选项加载器
 */
export const optionLoaders = {
  businessProductType: (
    optionsRef: Ref<{ label: string; value: string }[]>,
    onError?: (error: any, message: string) => void
  ) => loadStaticOptions("business_product_type", optionsRef, "加载业务产品类型选项失败", onError),

  incomeType: (
    optionsRef: Ref<{ label: string; value: string }[]>,
    onError?: (error: any, message: string) => void
  ) => loadStaticOptions("income_type", optionsRef, "加载收入分类选项失败", onError),

  currencyType: (
    optionsRef: Ref<{ label: string; value: string }[]>,
    onError?: (error: any, message: string) => void
  ) => loadStaticOptions("currency_type", optionsRef, "加载货币类型选项失败", onError),

  taxRateType: (
    optionsRef: Ref<{ label: string; value: string }[]>,
    onError?: (error: any, message: string) => void
  ) => loadStaticOptions("charge_tax_type", optionsRef, "加载税率类型选项失败", onError),

  signContractEntity: (
    optionsRef: Ref<{ label: string; value: string }[]>,
    onError?: (error: any, message: string) => void
  ) => loadStaticOptions("sign_contract_entity", optionsRef, "加载签约主体选项失败", onError),

  adjustReasonClass: (
    optionsRef: Ref<{ label: string; value: string }[]>,
    onError?: (error: any, message: string) => void
  ) => loadStaticOptions("adjust_reason_class", optionsRef, "加载调账原因分类选项失败", onError),
};
