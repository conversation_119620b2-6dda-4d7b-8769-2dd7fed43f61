import { ApiListResponse, ApiResponse } from "../types/api";
import type {
  InvoiceInfoItem,
  InvoiceFormData,
  InvoiceSearchParams,
  PostPaidInvoiceItem,
  PostPaidInvoiceSearchParams,
} from "../types/invoice";
import api from "./api";

export const getInvoiceList = async (
  params: InvoiceSearchParams
): Promise<ApiListResponse<InvoiceInfoItem[]>> => {
  const response = await api.get("/contract/invoices-info", { params });
  return response.data;
};

export const createInvoice = async (data: InvoiceFormData): Promise<any> => {
  const response = await api.post("/contract/invoices-info", data);
  return response.data;
};

export const updateInvoice = async (
  id: number,
  data: InvoiceFormData
): Promise<any> => {
  const response = await api.put(`/contract/invoices-info/${id}`, data);
  return response.data;
};

export const getInvoiceDetail = async (
  id: number
): Promise<ApiResponse<InvoiceInfoItem>> => {
  const response = await api.get<ApiResponse<InvoiceInfoItem>>(
    `/contract/invoices-info/${id}`
  );
  return response.data;
};

// 获取后付费发票列表
export const getPostPaidInvoiceList = async (
  params: PostPaidInvoiceSearchParams
): Promise<ApiListResponse<PostPaidInvoiceItem[]>> => {
  const response = await api.get("/post-paid-invoice", { params });
  return response.data;
};

// 批量预开票请求数据接口
export interface BatchPreInvoiceRequest {
  customer_id: number;
  account_seq: string;
  start_charge_month: number;
  end_charge_month: number;
}

// 批量预开票API
export const batchPreInvoice = async (
  data: BatchPreInvoiceRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/post-paid-invoice/batch-pre-invoice", data);
  return response.data;
};
