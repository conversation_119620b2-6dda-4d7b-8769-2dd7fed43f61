import { ApiResponse, ApiListResponse } from "../types/api";
import { User } from "../types/user";
import api from "./api";

export interface UserParams {
  page?: number;
  pageSize?: number;
  word?: string;
}

export const getDepartmentUsers = async (
  departmentId: number,
  params: UserParams
): Promise<ApiListResponse<User[]>> => {
  const response = await api.get(`/departments/${departmentId}/users`, {
    params: {
      page: params.page,
      pageSize: params.pageSize,
      word: params.word,
    },
  });
  return response.data;
};

export const getUsers = async (
  params: UserParams
): Promise<ApiListResponse<User[]>> => {
  const response = await api.get("/users", { params });
  return response.data;
};

export interface CreateUserRequest {
  username: string;
  email: string;
  mobile: string;
  password: string;
  role_ids: number[];
}

export const createUser = async (
  user: CreateUserRequest,
  departmentId: number
): Promise<ApiResponse<User>> => {
  const response = await api.post<ApiResponse<User>>(
    `/departments/${departmentId}/users`,
    user
  );
  return response.data;
};

export interface UpdateUserRequest {
  username: string;
  email: string;
  mobile: string;
  role_ids: number[];
}

export const updateUser = async (
  id: number,
  departmentId: number,
  user: UpdateUserRequest
): Promise<ApiResponse<User>> => {
  const response = await api.put<ApiResponse<User>>(`/departments/${departmentId}/users/${id}`, user);
  return response.data;
};
