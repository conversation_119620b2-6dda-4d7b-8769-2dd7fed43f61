export interface OrderItem {
  id: number;
  order_type: string;
  order_class: number;
  service_status: string;
  bill_status: string;
  job_status: string;
  order_contract_period: number;
  order_start_year: Date;
  order_remark: string;
  business_product_type: string;
  order_num: string;
  sub_order_num?: string;
  charge_order_num: string;
  total_num: string;
  service_type: string;
  pre_order_total_num: string;
  income_type: string;
  product_main_category: string;
  product_sub_category: string;
  product_scheme: string;
  product_after_sale: string;
  product_upload_file: string;
  pay_cycle: string;
  pay_type: string;
  account_seq: string;
  a_info: string;
  a_address: string;
  z_info: string;
  z_address: string;
  partner_name: string;
  partner_po_num: string;
  new_required_finished_date: Date;
  reality_bill_start_date: Date;
  new_build_start_time: any;
  new_build_finished_time: any;
  new_build_bill_time: Date;
  new_build_charge_time: Date;
  new_build_support_time: Date;
  remove_required_finished_date: Date;
  reality_bill_end_date: Date;
  remove_build_start_time: Date;
  remove_build_finished_time: Date;
  remove_build_bill_time: Date;
  remove_build_charge_time: Date;
  remove_build_support_time: Date;
  finished_remark: string;
  finished_file: string;
  group_approve_state: string;
  contract_num: string;
  customer_num: string;
  contract_title: string;
  create_user: string;
  created_at: string;
  income_fee_package_id: number;
  once_fee: number;
  cycle_fee: number;
  tax_rate: number;
  tax_type: string;
  currency_type: string;
  charge_explain: string;
  charge_remark: string;
}

export interface OrderFormData
  extends Omit<
    OrderItem,
    | "id"
    | "created_at"
    | "create_user"
    | "group_approve_state"
    | "service_status"
    | "bill_status"
    | "job_status"
    | "total_num"
    | "charge_order_num"
    | "order_num"
    | "reality_bill_start_date"
    | "new_build_bill_time"
    | "new_build_charge_time"
    | "new_build_support_time"
    | "remove_required_finished_date"
    | "reality_bill_end_date"
    | "remove_build_start_time"
    | "remove_build_finished_time"
    | "remove_build_bill_time"
    | "remove_build_charge_time"
    | "remove_build_support_time"
    | "pre_order_total_num"
    | "contract_title"
  > {}

export interface ProcessNode {
  label: string;
  status: "inactive" | "active" | "completed";
  date?: string;
  description?: string;
}
