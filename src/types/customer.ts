export interface CustomersInfoItem {
  id?: number;
  customer_num: string;
  customer_name: string;
  customer_name_intl: string;
  group_name: string;
  customer_usci: string;
  like_key: string;
  sale_name: string;
  customer_class: string;
  customer_type: string;
  trade_type: string;
  business: string;
  remark: string;
  upload_file: string;
  state: string;
  country: string;
  province: string;
  city: string;
  approve_state: string;
  approve_user: string;
  create_user: string;
  created_at: string;
}

export interface CustomerFormData
  extends Omit<
    CustomersInfoItem,
    "created_at" | "create_user" | "approve_user" | "approve_state"
  > {}

export interface SaleInfo {
  sale_name: string;
}

// 添加客户联系人接口定义
export interface ContactInfo {
  id?: number;
  name: string;
  company: string;
  position: string;
  address: string;
  phone: string;
  mobile_phone: string;
  email: string;
  contact_class: string;
  contact_type: string;
  own_type: string;
  remark: string;
  state: string;
}

export interface ContactFormData extends Omit<ContactInfo, "state"> {}

// 客户简单信息（下拉列表用）
export interface CustomerSimpleInfo {
  id: number;
  customer_name: string;
  customer_num: string;
}

// 分账序号信息
export interface AccountSeqInfo {
  id: number;
  seq_name: string;
  account_seq: string;
  customer_name: string;
  customer_num: string;
  tax: number;
  created_at: string;
}

export interface AccountSeqSimpleInfo {
  id: number;
  account_seq: string;
}

// 客户审批历史记录
export interface CustomerApproveHistoryItem {
  action: string;
  from_state: string;
  to_state: string;
  reason: string;
  operator: string;
  created_at: string;
}
