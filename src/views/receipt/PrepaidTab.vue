<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { useToast } from "primevue/usetoast";
import {
  getPrepaidStatements,
  submitPrepaidRecognition,
} from "../../services/bankStatement";
import type {
  BankStatementItem,
  PrepaidStatementItem,
  PrepaidRecognitionRequest,
  PrepaidRecognitionRequestItem,
} from "../../types/bankStatement";
import { formatCurrency } from "../../utils/common";

// Props
const props = defineProps<{
  bankStatementId: number;
  refreshKey?: number;
  bankStatementDetail?: BankStatementItem | null;
}>();

// Emits
const emit = defineEmits<{
  refreshData: [];
  updatePrepaidAmount: [amount: number];
}>();

const toast = useToast();

// 银行流水详情（从父组件传递）
const bankStatementDetail = computed(() => props.bankStatementDetail);

// 预付费认款数据
const prepaidStatements = ref<PrepaidStatementItem[]>([]);
const selectedPrepaidStatements = ref<PrepaidStatementItem[]>([]);
const prepaidLoading = ref(false);
const prepaidTotalRecords = ref(0);
const prepaidLazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选参数
const prepaidFilters = ref({
  order_num: "",
});

// 认款金额输入框数据
const prepaidAmounts = ref<Record<string, number>>({});

// 认款提交状态
const isSubmittingPrepaidRecognition = ref(false);

// 确认对话框状态
const confirmDialogVisible = ref(false);

// 计算当前选中的认款总金额
const selectedPrepaidAmount = computed(() => {
  return selectedPrepaidStatements.value.reduce((total, item) => {
    const amount = prepaidAmounts.value[item.total_num] || 0;
    return total + amount;
  }, 0);
});

// 计算当前认款总金额
const currentPrepaidRecognitionAmount = computed(() => {
  const selectedAmount = selectedPrepaidStatements.value.reduce(
    (total, item) => {
      const amount = prepaidAmounts.value[item.total_num] || 0;
      return total + amount;
    },
    0
  );

  // 加上银行流水详情中的确认中金额
  const bankConfirmingAmount = bankStatementDetail.value
    ? parseFloat(bankStatementDetail.value.confirming_amount) || 0
    : 0;

  return selectedAmount + bankConfirmingAmount;
});

// 计算认款按钮是否应该禁用
const isPrepaidRecognitionDisabled = computed(() => {
  // 没有选中任何项
  if (selectedPrepaidStatements.value.length === 0) return true;

  // 检查选中的认款记录中是否有零值金额
  const hasZeroAmount = selectedPrepaidStatements.value.some((item) => {
    const amount = prepaidAmounts.value[item.total_num] || 0;
    return amount === 0;
  });
  if (hasZeroAmount) return true;

  // 当前认款金额大于银行流水总金额
  if (!bankStatementDetail.value) return true;
  const totalAmount = parseFloat(bankStatementDetail.value.amount) || 0;
  return currentPrepaidRecognitionAmount.value > totalAmount;
});

// 监听选中项变化，确保选中项有正确的默认值
watch(
  selectedPrepaidStatements,
  (newSelection) => {
    newSelection.forEach((item) => {
      if (!(item.total_num in prepaidAmounts.value)) {
        // 预付费认款默认金额设为0，用户需要手动输入
        prepaidAmounts.value[item.total_num] = 0;
      }
    });
  },
  { deep: true }
);

// 监听refreshKey变化，重新加载数据
watch(
  () => props.refreshKey,
  () => {
    loadPrepaidStatements();
  }
);

// 监听预付费认款金额变化，通知父组件
watch(
  selectedPrepaidAmount,
  (newAmount) => {
    emit("updatePrepaidAmount", newAmount);
  },
  { immediate: true }
);

// 加载预付费认款列表
const loadPrepaidStatements = async () => {
  prepaidLoading.value = true;
  try {
    const params: { page: number; pageSize: number; order_num?: string } = {
      page: prepaidLazyParams.value.page,
      pageSize: prepaidLazyParams.value.pageSize,
    };

    // 添加筛选参数
    if (prepaidFilters.value.order_num.trim()) {
      params.order_num = prepaidFilters.value.order_num.trim();
    }

    const response = await getPrepaidStatements(props.bankStatementId, params);
    prepaidStatements.value = response.data.records;
    prepaidTotalRecords.value = response.data.page.total;

    // 为所有行初始化默认的金额值
    response.data.records.forEach((item) => {
      if (!(item.total_num in prepaidAmounts.value)) {
        prepaidAmounts.value[item.total_num] = 0;
      }
    });
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载预付费认款列表失败",
      life: 3000,
    });
  } finally {
    prepaidLoading.value = false;
  }
};

// 处理分页
const onPrepaidPage = (event: { page: number; rows: number }) => {
  prepaidLazyParams.value.page = event.page + 1;
  prepaidLazyParams.value.pageSize = event.rows;
  loadPrepaidStatements();
};

// 搜索预付费认款
const handlePrepaidSearch = () => {
  prepaidLazyParams.value.page = 1;
  loadPrepaidStatements();
};

// 重置筛选
const resetPrepaidFilters = () => {
  prepaidFilters.value = {
    order_num: "",
  };
  prepaidLazyParams.value.page = 1;
  loadPrepaidStatements();
};

// 显示确认对话框
const showPrepaidConfirmDialog = () => {
  if (isPrepaidRecognitionDisabled.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "当前认款总金额不能大于银行流水剩余的总金额",
      life: 3000,
    });
    return;
  }

  confirmDialogVisible.value = true;
};

// 提交预付费认款
const handlePrepaidRecognitionSubmit = async () => {
  isSubmittingPrepaidRecognition.value = true;
  try {
    // 构建请求数据
    const prepaidItems: PrepaidRecognitionRequestItem[] =
      selectedPrepaidStatements.value.map((item) => ({
        amount: (prepaidAmounts.value[item.total_num] || 0).toString(),
        sub_order_no: item.total_num,
      }));

    const requestData: PrepaidRecognitionRequest = {
      prepaid: prepaidItems,
    };

    await submitPrepaidRecognition(props.bankStatementId, requestData);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "预付费认款提交成功",
      life: 3000,
    });

    // 清空选中项和认款金额
    selectedPrepaidStatements.value = [];

    // 重新加载数据
    loadPrepaidStatements();

    // 触发父组件刷新银行流水详情
    emit("refreshData");

    // 重置预付费认款金额
    emit("updatePrepaidAmount", 0);
  } catch (error: any) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "预付费认款提交失败",
      life: 3000,
    });
  } finally {
    isSubmittingPrepaidRecognition.value = false;
    confirmDialogVisible.value = false;
  }
};

onMounted(() => {
  loadPrepaidStatements();
});
</script>

<template>
  <div class="prepaid-tab">
    <!-- 筛选区域 -->
    <Toolbar class="mb-2">
      <template #start>
        <i class="pi pi-user text-primary mr-2"></i>
        <span class="font-semibold text-primary">
          {{ bankStatementDetail?.customer_name || "未指定客户" }}
        </span>
      </template>
      <template #end>
        <div class="flex flex-wrap align-items-center gap-2 mr-2">
          <FloatLabel>
            <label for="filterPrepaidOrderNum">订单编号</label>
            <InputText
              id="filterPrepaidOrderNum"
              v-model="prepaidFilters.order_num"
            />
          </FloatLabel>
        </div>
        <Button
          label="搜索"
          icon="pi pi-search"
          @click="handlePrepaidSearch"
          class="p-button-sm mr-2"
        />
        <Button
          label="重置"
          icon="pi pi-refresh"
          @click="resetPrepaidFilters"
          outlined
          class="p-button-sm"
        />
        <Divider layout="vertical" />
        <Button
          icon="fa fa-regular fa-handshake"
          @click="showPrepaidConfirmDialog"
          :disabled="isPrepaidRecognitionDisabled"
          :loading="isSubmittingPrepaidRecognition"
          severity="warn"
        />
      </template>
    </Toolbar>

    <DataTable
      :value="prepaidStatements"
      v-model:selection="selectedPrepaidStatements"
      :lazy="true"
      :paginator="true"
      :rows="20"
      :rowsPerPageOptions="[10, 20, 50]"
      :totalRecords="prepaidTotalRecords"
      :loading="prepaidLoading"
      @page="onPrepaidPage($event)"
      showGridlines
      stripedRows
      scrollable
      scrollHeight="calc(100vh - 33rem)"
    >
      <template #empty>
        <div class="empty-message">
          <i
            class="pi pi-inbox"
            style="
              font-size: 2rem;
              color: var(--p-text-color-secondary);
              margin-bottom: 1rem;
            "
          ></i>
          <p>暂无预付费认款数据</p>
        </div>
      </template>
      <Column
        selectionMode="multiple"
        headerStyle="width: 3rem"
        alignFrozen="left"
        frozen
      />
      <Column
        header="认款金额"
        style="min-width: 8rem; max-width: 8rem;"
        alignFrozen="left"
        frozen
      >
        <template #body="slotProps">
          <InputNumber
            v-model="prepaidAmounts[slotProps.data.total_num]"
            :disabled="
              !selectedPrepaidStatements.some(
                (item) => item.total_num === slotProps.data.total_num
              )
            "
            mode="currency"
            currency="CNY"
            :min="0"
            placeholder="请输入认款金额"
            class="prepaid-amount-input"
          />
        </template>
      </Column>
      <Column
        field="order_num"
        header="订单编号"
        style="min-width: 12rem"
      />
      <Column
        field="total_num"
        header="合成编号"
        style="min-width: 15rem"
      />
      <Column
        field="income_type"
        header="收入类型"
        style="min-width: 8rem"
      />
      <Column
        field="account_seq"
        header="分账序号"
        style="min-width: 10rem"
      />
      <Column
        field="pay_type"
        header="付费方式"
        style="min-width: 8rem"
      />
      <Column
        field="bill_status"
        header="计费状态"
        style="min-width: 8rem"
      />
    </DataTable>

    <!-- 确认认款对话框 -->
    <Dialog
      v-model:visible="confirmDialogVisible"
      modal
      header="确认预付费认款"
      :style="{ width: '25rem' }"
      :closable="!isSubmittingPrepaidRecognition"
      :closeOnEscape="!isSubmittingPrepaidRecognition"
    >
      <div class="confirmation-content">
        <div class="mb-3 flex items-center">
          <i
            class="pi pi-exclamation-triangle text-orange-500 mr-2"
            style="font-size: 2rem"
          ></i>
          <span class="font-semibold flex">确定要提交预付费认款吗？</span>
        </div>
        <div class="confirmation-details">
          <div class="detail-item">
            <label>认款金额：</label>
            <span class="font-semibold text-primary">
              {{
                formatCurrency(
                  selectedPrepaidAmount.toString(),
                  bankStatementDetail?.currency_type || "CNY"
                )
              }}
            </span>
          </div>
          <div class="detail-item">
            <label>选中项目：</label>
            <span class="font-semibold"
              >{{ selectedPrepaidStatements.length }}项</span
            >
          </div>
        </div>
      </div>

      <template #footer>
        <Button
          label="取消"
          icon="pi pi-times"
          @click="confirmDialogVisible = false"
          :disabled="isSubmittingPrepaidRecognition"
          text
        />
        <Button
          label="确认认款"
          icon="pi pi-check"
          @click="handlePrepaidRecognitionSubmit"
          :loading="isSubmittingPrepaidRecognition"
          severity="warn"
        />
      </template>
    </Dialog>
  </div>
</template>

<style scoped>
.prepaid-tab {
  height: 100%;
}

/* 认款金额输入框样式 */
.prepaid-amount-input {
  width: 100%;
}

:deep(.prepaid-amount-input .p-inputnumber-input) {
  font-size: 0.875rem;
  padding: 0.5rem;
}

/* 确认对话框样式 */
.confirmation-content {
  padding: 0.5rem 0;
}

.confirmation-details {
  background: var(--p-surface-50);
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid var(--p-primary-color);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  color: var(--p-text-color-secondary);
  font-weight: 500;
}
</style>
