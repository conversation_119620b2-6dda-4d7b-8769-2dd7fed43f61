<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { useToast } from "primevue/usetoast";
import {
  getUnrecognizedStatements,
  submitUnrecognizedApproval,
  revokeUnrecognizedStatements,
} from "../../services/bankStatement";
import type { UnrecognizedStatementItem } from "../../types/bankStatement";
import { formatDateTime } from "../../utils/common";

// Props
const props = defineProps<{
  bankStatementId: number;
  refreshKey: number;
}>();

// Emits
const emit = defineEmits<{
  refreshData: [];
}>();

const toast = useToast();

// 未认款数据
const unrecognizedStatements = ref<UnrecognizedStatementItem[]>([]);
const selectedUnrecognizedStatements = ref<UnrecognizedStatementItem[]>([]);
const unrecognizedLoading = ref(false);
const unrecognizedTotalRecords = ref(0);
const unrecognizedLazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 审批状态筛选
const approvalStateOptions = [
  { label: "待提交审批", value: 0 },
  { label: "审批中", value: 1 },
  { label: "审批拒绝", value: 3 },
];
const selectedApprovalState = ref(0); // 默认选择待提交审批

// 批量操作状态
const isSubmittingApproval = ref(false);
const isRevoking = ref(false);

// 加载未认款列表
const loadUnrecognizedStatements = async () => {
  unrecognizedLoading.value = true;
  try {
    const response = await getUnrecognizedStatements(props.bankStatementId, {
      page: unrecognizedLazyParams.value.page,
      pageSize: unrecognizedLazyParams.value.pageSize,
      state: selectedApprovalState.value,
    });
    unrecognizedStatements.value = response.data.records;
    unrecognizedTotalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载未认款列表失败",
      life: 3000,
    });
  } finally {
    unrecognizedLoading.value = false;
  }
};

// 处理分页
const onUnrecognizedPage = (event: { page: number; rows: number }) => {
  unrecognizedLazyParams.value.page = event.page + 1;
  unrecognizedLazyParams.value.pageSize = event.rows;
  loadUnrecognizedStatements();
};

// 批量提交审批
const handleSubmitApproval = async () => {
  if (selectedUnrecognizedStatements.value.length === 0) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请选择要提交审批的记录",
      life: 3000,
    });
    return;
  }

  isSubmittingApproval.value = true;
  try {
    const tmpIds = selectedUnrecognizedStatements.value.map((item) => item.id);
    await submitUnrecognizedApproval(props.bankStatementId, {
      tmp_ids: tmpIds,
    });

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "提交审批成功",
      life: 3000,
    });

    // 清空选中项并重新加载数据
    selectedUnrecognizedStatements.value = [];
    loadUnrecognizedStatements();

    // 触发父组件刷新银行流水详情和待认款列表
    emit("refreshData");
  } catch (error: any) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "提交审批失败",
      life: 3000,
    });
  } finally {
    isSubmittingApproval.value = false;
  }
};

// 批量撤销
const handleRevoke = async () => {
  if (selectedUnrecognizedStatements.value.length === 0) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请选择要撤销的记录",
      life: 3000,
    });
    return;
  }

  isRevoking.value = true;
  try {
    const tmpIds = selectedUnrecognizedStatements.value.map((item) => item.id);
    await revokeUnrecognizedStatements(props.bankStatementId, {
      tmp_ids: tmpIds,
    });

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "撤销成功",
      life: 3000,
    });

    // 清空选中项并重新加载数据
    selectedUnrecognizedStatements.value = [];
    loadUnrecognizedStatements();

    // 触发父组件刷新银行流水详情和待认款列表
    emit("refreshData");
  } catch (error: any) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "撤销失败",
      life: 3000,
    });
  } finally {
    isRevoking.value = false;
  }
};

// 获取审批状态显示文本
const getApprovalStateText = (state: number) => {
  switch (state) {
    case 0:
      return "待提交审批";
    case 1:
      return "审批中";
    case 3:
      return "审批拒绝";
    default:
      return "未知";
  }
};

// 获取审批状态样式
const getApprovalStateSeverity = (state: number) => {
  switch (state) {
    case 0:
      return "warn";
    case 1:
      return "success";
    case 3:
      return "danger";
    default:
      return "secondary";
  }
};

// 监听审批状态变化，重新加载数据
watch(selectedApprovalState, () => {
  unrecognizedLazyParams.value.page = 1; // 重置到第一页
  selectedUnrecognizedStatements.value = []; // 清空选中项
  loadUnrecognizedStatements();
});

// 监听 refreshKey 变化，触发数据刷新
watch(
  () => props.refreshKey,
  (newKey, oldKey) => {
    // 只有在 refreshKey 真正变化时才刷新（避免初始化时的触发）
    if (oldKey !== undefined && newKey !== oldKey) {
      loadUnrecognizedStatements();
      // 清空选中项
      selectedUnrecognizedStatements.value = [];
    }
  }
);

onMounted(() => {
  loadUnrecognizedStatements();
});
</script>

<template>
  <div class="unrecognized-tab">
    <!-- 工具栏 -->
    <Toolbar class="mb-3">
      <template #start>
        <div class="flex items-center gap-2">
          <i class="pi pi-clock text-orange-500"></i>
          <span class="font-semibold text-orange-500 flex"
            >未审批的认款记录</span
          >
          <Badge
            v-if="selectedUnrecognizedStatements.length > 0"
            :value="selectedUnrecognizedStatements.length"
            severity="info"
          />
        </div>
      </template>
      <template #end>
        <div class="flex gap-2">
          <Select
            v-model="selectedApprovalState"
            :options="approvalStateOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="选择审批状态"
            @change="loadUnrecognizedStatements"
            class="w-50"
          />
          <Button
            label="提交审批"
            icon="pi pi-check"
            @click="handleSubmitApproval"
            :disabled="
              selectedUnrecognizedStatements.length === 0 ||
              selectedApprovalState === 1
            "
            :loading="isSubmittingApproval"
            severity="success"
            size="small"
          />
          <Button
            label="撤销"
            icon="pi pi-times"
            @click="handleRevoke"
            :disabled="
              selectedUnrecognizedStatements.length === 0 ||
              selectedApprovalState === 1
            "
            :loading="isRevoking"
            severity="danger"
            size="small"
            outlined
          />
        </div>
      </template>
    </Toolbar>

    <!-- 数据表格 -->
    <DataTable
      :value="unrecognizedStatements"
      v-model:selection="selectedUnrecognizedStatements"
      :lazy="true"
      :paginator="true"
      :rows="20"
      :rowsPerPageOptions="[10, 20, 50]"
      :totalRecords="unrecognizedTotalRecords"
      :loading="unrecognizedLoading"
      @page="onUnrecognizedPage($event)"
      showGridlines
      stripedRows
      scrollable
      scrollHeight="calc(100vh - 35rem)"
    >
      <template #empty>
        <div class="empty-message">
          <i
            class="pi pi-inbox"
            style="
              font-size: 2rem;
              color: var(--p-text-color-secondary);
              margin-bottom: 1rem;
            "
          ></i>
          <p>暂无未认款数据</p>
        </div>
      </template>
      <Column
        selectionMode="multiple"
        headerStyle="width: 3rem"
        alignFrozen="left"
        frozen
      />
      <Column field="amount" header="金额" style="min-width: 8rem">
        <template #body="slotProps">
          <span class="font-semibold text-primary">
            ¥{{ parseFloat(slotProps.data.amount).toLocaleString() }}
          </span>
        </template>
      </Column>

      <Column field="sub_order_no" header="子订单号" style="min-width: 15rem" />

      <Column field="receive_type" header="收款类型" style="min-width: 8rem" />

      <Column
        field="group_approve_state"
        header="审批状态"
        style="min-width: 8rem"
      >
        <template #body="slotProps">
          <Tag
            :value="getApprovalStateText(slotProps.data.group_approve_state)"
            :severity="
              getApprovalStateSeverity(slotProps.data.group_approve_state)
            "
          />
        </template>
      </Column>

      <Column field="created_at" header="创建时间" style="min-width: 12rem">
        <template #body="slotProps">
          <span>{{ formatDateTime(slotProps.data.created_at) }}</span>
        </template>
      </Column>
    </DataTable>
  </div>
</template>

<style scoped>
.unrecognized-tab {
  height: 100%;
}

.filter-toolbar {
  padding: 1rem;
  background-color: var(--p-surface-card);
  border: 1px solid var(--p-surface-border);
  border-radius: 6px;
}
</style>
