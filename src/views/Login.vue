<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useToast } from "primevue/usetoast";
import { login } from "../services/auth";

const router = useRouter();
const toast = useToast();

const email = ref("");
const password = ref("");
const loading = ref(false);
const emailError = ref("");
const passwordError = ref("");

const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email.value) {
    emailError.value = "请输入邮箱地址";
    return false;
  }
  if (!emailRegex.test(email.value)) {
    emailError.value = "请输入有效的邮箱地址";
    return false;
  }
  emailError.value = "";
  return true;
};

const validatePassword = () => {
  if (!password.value) {
    passwordError.value = "请输入密码";
    return false;
  }
  if (password.value.length < 6) {
    passwordError.value = "密码长度至少为6位";
    return false;
  }
  passwordError.value = "";
  return true;
};

const passwordStrength = computed(() => {
  if (!password.value) return 0;
  let strength = 0;
  if (password.value.length >= 8) strength++;
  if (/[A-Z]/.test(password.value)) strength++;
  if (/[a-z]/.test(password.value)) strength++;
  if (/[0-9]/.test(password.value)) strength++;
  if (/[^A-Za-z0-9]/.test(password.value)) strength++;
  return strength;
});

const strengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength <= 1) return "weak";
  if (strength <= 3) return "medium";
  return "strong";
});

const handleLogin = async () => {
  const isEmailValid = validateEmail();
  const isPasswordValid = validatePassword();

  if (!isEmailValid || !isPasswordValid) {
    return;
  }

  try {
    loading.value = true;
    const response = await login(email.value, password.value);

    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "登录成功",
        life: 3000,
      });
      router.push("/");
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message,
        life: 3000,
      });
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "登录失败，请稍后重试",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

const goToForgotPassword = () => {
  router.push("/forgot-password");
};
</script>

<template>
  <div class="login-container">
    <!-- 背景和3D插图 -->
    <div class="background-visual">
      <div class="visual-illustration">
        <!-- 3D风格的插图元素 -->
        <div class="illustration-container">
          <div class="floating-shapes">
            <div class="shape shape-cube"></div>
            <div class="shape shape-sphere"></div>
            <div class="shape shape-cylinder"></div>
            <div class="shape shape-pyramid"></div>
          </div>
          <div class="chart-elements">
            <div class="chart-bar chart-1"></div>
            <div class="chart-bar chart-2"></div>
            <div class="chart-bar chart-3"></div>
          </div>
          <div class="floating-particles">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮的登录卡片 -->
    <div class="login-card-container">
      <div class="login-card">
        <!-- 品牌标识 -->
        <div class="brand-section">
          <div class="brand-logo">
            <i
              class="pi pi-chart-line"
              style="font-size: 1.5rem; color: #007aff"
            ></i>
            <span class="brand-name">财务系统</span>
          </div>
          <div class="auth-switch">
            <span class="auth-text">没有账户？</span>
            <a href="#" class="auth-link">注册</a>
          </div>
        </div>

        <!-- 登录标题 -->
        <div class="form-header">
          <h3 class="form-title">欢迎登录</h3>
        </div>

        <!-- 登录表单 -->
        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-field">
            <label for="email" class="field-label">邮箱地址</label>
            <InputText
              id="email"
              v-model="email"
              type="email"
              placeholder="请输入您的邮箱地址"
              @blur="validateEmail"
              :class="{ 'p-invalid': emailError }"
              class="w-full"
              required
            />
            <small class="error-message" v-if="emailError">{{
              emailError
            }}</small>
          </div>
          <div class="form-field">
            <div class="password-header">
              <label for="password" class="field-label">密码</label>
              <Button
                type="button"
                label="忘记密码？"
                link
                @click="goToForgotPassword"
                class="forgot-password-link"
              />
            </div>
            <Password
              id="password"
              v-model="password"
              placeholder="请输入您的密码"
              :feedback="false"
              toggleMask
              @blur="validatePassword"
              :class="{ 'p-invalid': passwordError }"
              class="w-full"
              required
            />
            <small class="error-message" v-if="passwordError">{{
              passwordError
            }}</small>
            <div class="password-strength" v-if="password">
              <div class="strength-indicator" :class="strengthClass"></div>
            </div>
          </div>

          <Button
            type="submit"
            label="登录"
            :loading="loading"
            class="login-btn"
            size="large"
          />
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器 - 全屏背景 */
.login-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #f8f4ff 0%, #e8f4fd 50%, #fff0f8 100%);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  overflow: hidden;
}

/* 背景视觉区域 */
.background-visual {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.visual-illustration {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 3D插图容器 */
.illustration-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

/* 浮动3D形状 */
.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 8px;
  animation: float3d 6s ease-in-out infinite;
}

.shape-cube {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  top: 20%;
  right: 15%;
  animation-delay: 0s;
  transform: perspective(100px) rotateX(15deg) rotateY(15deg);
}

.shape-sphere {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 50%;
  top: 60%;
  right: 25%;
  animation-delay: 1s;
}

.shape-cylinder {
  width: 40px;
  height: 100px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 20px;
  bottom: 30%;
  right: 10%;
  animation-delay: 2s;
}

.shape-pyramid {
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 60px solid #ffeaa7;
  top: 40%;
  right: 35%;
  animation-delay: 3s;
}

/* 图表元素 */
.chart-elements {
  position: absolute;
  bottom: 20%;
  right: 20%;
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.chart-bar {
  width: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  animation: chartGrow 3s ease-in-out infinite;
}

.chart-1 {
  height: 40px;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  animation-delay: 0s;
}

.chart-2 {
  height: 60px;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  animation-delay: 0.5s;
}

.chart-3 {
  height: 80px;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  animation-delay: 1s;
}

/* 浮动粒子 */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat 8s ease-in-out infinite;
}

.particle-1 {
  width: 8px;
  height: 8px;
  top: 15%;
  left: 20%;
  animation-delay: 0s;
}

.particle-2 {
  width: 12px;
  height: 12px;
  top: 35%;
  left: 80%;
  animation-delay: 1s;
}

.particle-3 {
  width: 6px;
  height: 6px;
  top: 70%;
  left: 15%;
  animation-delay: 2s;
}

.particle-4 {
  width: 10px;
  height: 10px;
  top: 80%;
  left: 70%;
  animation-delay: 3s;
}

.particle-5 {
  width: 14px;
  height: 14px;
  top: 50%;
  left: 10%;
  animation-delay: 4s;
}

/* 悬浮登录卡片容器 */
.login-card-container {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 2rem;
  padding-left: 18%;
}

/* 登录卡片 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: cardFloat 6s ease-in-out infinite;
}

/* 品牌区域 */
.brand-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1d1d1f;
}

.auth-switch {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-text {
  font-size: 0.875rem;
  color: #86868b;
}

.auth-link {
  font-size: 0.875rem;
  color: #007aff;
  text-decoration: none;
  font-weight: 500;
}

.auth-link:hover {
  color: #0051d5;
}

/* 表单标题 */
.form-header {
  margin-bottom: 2rem;
}

.form-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0;
  letter-spacing: -0.02em;
}

/* 表单样式 */
.login-form {
  width: 100%;
}

.form-field {
  margin-bottom: 1.5rem;
}

.field-label {
  display: block;
  margin-bottom: 0.75rem;
  color: #1d1d1f;
  font-weight: 500;
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-password-link :deep(.p-button-label) {
  color: #007aff;
  font-size: 0.9rem;
  font-weight: 500;
}

.forgot-password-link:hover :deep(.p-button-label) {
  color: #0051d5;
}

/* 输入框样式 */
.form-field :deep(.p-inputtext),
.form-field :deep(.p-password-input) {
  width: 100%;
  height: 52px;
  border: 2px solid #e5e5e7;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 1rem;
  background: #ffffff;
  transition: all 0.3s ease;
}

.form-field :deep(.p-inputtext:enabled:hover),
.form-field :deep(.p-password-input:enabled:hover) {
  border-color: #007aff;
}

.form-field :deep(.p-inputtext:enabled:focus),
.form-field :deep(.p-password-input:enabled:focus) {
  border-color: #007aff;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
  outline: none;
}

.form-field :deep(.p-invalid) {
  border-color: #ff3b30;
}

.error-message {
  color: #ff3b30;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: block;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 0.75rem;
  height: 4px;
  border-radius: 2px;
  background-color: #f2f2f7;
  overflow: hidden;
}

.strength-indicator {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-indicator.weak {
  width: 33.33%;
  background-color: #ff3b30;
}

.strength-indicator.medium {
  width: 66.66%;
  background-color: #ff9500;
}

.strength-indicator.strong {
  width: 100%;
  background-color: #34c759;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 52px;
  background: #1d1d1f;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-top: 1rem;
}

.login-btn:hover {
  background: #2d2d2f;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(29, 29, 31, 0.3);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:disabled {
  background: #d2d2d7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 动画效果 */
@keyframes float3d {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) translateX(10px);
    opacity: 1;
  }
}

@keyframes chartGrow {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.3);
  }
}

@keyframes cardFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-card-container {
    justify-content: center;
    padding: 1.5rem;
  }

  .login-card {
    max-width: 100%;
    padding: 2rem;
  }

  .form-title {
    font-size: 2rem;
  }

  .brand-section {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .login-card-container {
    padding: 1rem;
  }

  .login-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .form-title {
    font-size: 1.75rem;
  }
}
</style>
