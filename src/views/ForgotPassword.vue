<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useToast } from "primevue/usetoast";

const router = useRouter();
const toast = useToast();

const email = ref("");
const newPassword = ref("");
const confirmPassword = ref("");
const verificationCode = ref("");
const showResetForm = ref(false);
const emailError = ref("");
const passwordError = ref("");
const confirmPasswordError = ref("");
const verificationCodeError = ref("");

const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email.value) {
    emailError.value = "请输入邮箱地址";
    return false;
  }
  if (!emailRegex.test(email.value)) {
    emailError.value = "请输入有效的邮箱地址";
    return false;
  }
  emailError.value = "";
  return true;
};

const validatePassword = () => {
  if (!newPassword.value) {
    passwordError.value = "请输入新密码";
    return false;
  }
  if (newPassword.value.length < 6) {
    passwordError.value = "密码长度至少为6位";
    return false;
  }
  passwordError.value = "";
  return true;
};

const validateConfirmPassword = () => {
  if (!confirmPassword.value) {
    confirmPasswordError.value = "请确认新密码";
    return false;
  }
  if (confirmPassword.value !== newPassword.value) {
    confirmPasswordError.value = "两次输入的密码不一致";
    return false;
  }
  confirmPasswordError.value = "";
  return true;
};

const validateVerificationCode = () => {
  if (!verificationCode.value) {
    verificationCodeError.value = "请输入验证码";
    return false;
  }
  if (!/^\d{6}$/.test(verificationCode.value)) {
    verificationCodeError.value = "验证码格式不正确";
    return false;
  }
  verificationCodeError.value = "";
  return true;
};

const passwordStrength = computed(() => {
  if (!newPassword.value) return 0;
  let strength = 0;
  if (newPassword.value.length >= 8) strength++;
  if (/[A-Z]/.test(newPassword.value)) strength++;
  if (/[a-z]/.test(newPassword.value)) strength++;
  if (/[0-9]/.test(newPassword.value)) strength++;
  if (/[^A-Za-z0-9]/.test(newPassword.value)) strength++;
  return strength;
});

const strengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength <= 1) return "weak";
  if (strength <= 3) return "medium";
  return "strong";
});

const requestReset = () => {
  if (!validateEmail()) {
    return;
  }

  // TODO: 实现发送验证码逻辑
  toast.add({
    severity: "success",
    summary: "成功",
    detail: "验证码已发送到您的邮箱",
    life: 3000,
  });
  showResetForm.value = true;
};

const resetPassword = () => {
  const isEmailValid = validateEmail();
  const isPasswordValid = validatePassword();
  const isConfirmPasswordValid = validateConfirmPassword();
  const isVerificationCodeValid = validateVerificationCode();

  if (
    !isEmailValid ||
    !isPasswordValid ||
    !isConfirmPasswordValid ||
    !isVerificationCodeValid
  ) {
    return;
  }

  // TODO: 实现重置密码逻辑
  toast.add({
    severity: "success",
    summary: "成功",
    detail: "密码重置成功",
    life: 3000,
  });
  router.push("/login");
};

const goToLogin = () => {
  router.push("/login");
};
</script>

<template>
  <div class="forgot-password-container">
    <!-- 背景和3D插图 -->
    <div class="background-visual">
      <div class="visual-illustration">
        <!-- 安全主题的3D插图元素 -->
        <div class="illustration-container">
          <div class="security-shapes">
            <div class="shape shield-shape"></div>
            <div class="shape lock-shape"></div>
            <div class="shape key-shape"></div>
            <div class="shape check-shape"></div>
          </div>
          <div class="security-elements">
            <div class="security-bar security-1"></div>
            <div class="security-bar security-2"></div>
            <div class="security-bar security-3"></div>
          </div>
          <div class="floating-particles">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮的重置密码卡片 -->
    <div class="forgot-card-container">
      <div class="forgot-card">
        <!-- 品牌标识 -->
        <div class="brand-section">
          <div class="brand-logo">
            <i
              class="pi pi-shield"
              style="font-size: 1.5rem; color: #ff9500"
            ></i>
            <span class="brand-name">密码重置</span>
          </div>
          <div class="auth-switch">
            <span class="auth-text">记起密码了？</span>
            <a href="#" @click="goToLogin" class="auth-link">登录</a>
          </div>
        </div>

        <!-- 表单标题 -->
        <div class="form-header">
          <h1 class="form-title">
            {{ showResetForm ? "设置新密码" : "重置密码" }}
          </h1>
          <p class="form-subtitle">
            {{
              showResetForm
                ? "请输入您的新密码"
                : "请输入您的邮箱地址，我们将发送验证码"
            }}
          </p>
        </div>

        <!-- 重置密码表单 -->
        <form
          @submit.prevent="showResetForm ? resetPassword() : requestReset()"
          class="forgot-form"
        >
          <div class="form-field">
            <label for="email" class="field-label">邮箱地址</label>
            <InputText
              id="email"
              v-model="email"
              type="email"
              placeholder="请输入您的邮箱地址"
              :disabled="showResetForm"
              @blur="validateEmail"
              :class="{ 'p-invalid': emailError }"
              class="w-full"
              required
            />
            <small class="error-message" v-if="emailError">{{
              emailError
            }}</small>
          </div>

          <template v-if="showResetForm">
            <div class="form-field">
              <label for="verificationCode" class="field-label">验证码</label>
              <InputText
                id="verificationCode"
                v-model="verificationCode"
                placeholder="请输入6位验证码"
                @blur="validateVerificationCode"
                :class="{ 'p-invalid': verificationCodeError }"
                class="w-full"
                maxlength="6"
                required
              />
              <small class="error-message" v-if="verificationCodeError">{{
                verificationCodeError
              }}</small>
            </div>

            <div class="form-field">
              <label for="newPassword" class="field-label">新密码</label>
              <Password
                id="newPassword"
                v-model="newPassword"
                placeholder="请输入新密码"
                :feedback="false"
                toggleMask
                @blur="validatePassword"
                :class="{ 'p-invalid': passwordError }"
                class="w-full"
                required
              />
              <small class="error-message" v-if="passwordError">{{
                passwordError
              }}</small>
              <div class="password-strength" v-if="newPassword">
                <div class="strength-indicator" :class="strengthClass"></div>
              </div>
            </div>

            <div class="form-field">
              <label for="confirmPassword" class="field-label">确认密码</label>
              <Password
                id="confirmPassword"
                v-model="confirmPassword"
                placeholder="请再次输入新密码"
                :feedback="false"
                toggleMask
                @blur="validateConfirmPassword"
                :class="{ 'p-invalid': confirmPasswordError }"
                class="w-full"
                required
              />
              <small class="error-message" v-if="confirmPasswordError">{{
                confirmPasswordError
              }}</small>
            </div>
          </template>

          <Button
            type="submit"
            :label="showResetForm ? '重置密码' : '发送验证码'"
            class="submit-btn"
            size="large"
          />
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器 - 全屏背景 */
.forgot-password-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #fff4e6 0%, #ffe8cc 50%, #ffedd5 100%);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  overflow: hidden;
}

/* 背景视觉区域 */
.background-visual {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.visual-illustration {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 3D插图容器 */
.illustration-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

/* 安全主题3D形状 */
.security-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 8px;
  animation: securityFloat 6s ease-in-out infinite;
}

.shield-shape {
  width: 70px;
  height: 90px;
  background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
  border-radius: 35px 35px 0 35px;
  top: 15%;
  right: 20%;
  animation-delay: 0s;
  transform: perspective(100px) rotateX(10deg) rotateY(10deg);
}

.lock-shape {
  width: 50px;
  height: 60px;
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  border-radius: 8px;
  top: 55%;
  right: 15%;
  animation-delay: 1s;
}

.key-shape {
  width: 80px;
  height: 30px;
  background: linear-gradient(135deg, #ff7f50 0%, #ff6347 100%);
  border-radius: 15px;
  bottom: 35%;
  right: 25%;
  animation-delay: 2s;
}

.check-shape {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #32cd32 0%, #228b22 100%);
  border-radius: 50%;
  top: 35%;
  right: 35%;
  animation-delay: 3s;
}

/* 安全元素 */
.security-elements {
  position: absolute;
  bottom: 25%;
  right: 15%;
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.security-bar {
  width: 18px;
  background: rgba(255, 149, 0, 0.8);
  border-radius: 9px;
  animation: securityGrow 3s ease-in-out infinite;
}

.security-1 {
  height: 35px;
  background: linear-gradient(135deg, #ff9500 0%, #ffb347 100%);
  animation-delay: 0s;
}

.security-2 {
  height: 55px;
  background: linear-gradient(135deg, #ffa500 0%, #ff8c00 100%);
  animation-delay: 0.5s;
}

.security-3 {
  height: 75px;
  background: linear-gradient(135deg, #ff7f50 0%, #ff6347 100%);
  animation-delay: 1s;
}

/* 浮动粒子 */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  background: rgba(255, 149, 0, 0.6);
  border-radius: 50%;
  animation: particleFloat 8s ease-in-out infinite;
}

.particle-1 {
  width: 8px;
  height: 8px;
  top: 20%;
  left: 25%;
  animation-delay: 0s;
}

.particle-2 {
  width: 12px;
  height: 12px;
  top: 40%;
  left: 75%;
  animation-delay: 1s;
}

.particle-3 {
  width: 6px;
  height: 6px;
  top: 65%;
  left: 20%;
  animation-delay: 2s;
}

.particle-4 {
  width: 10px;
  height: 10px;
  top: 75%;
  left: 65%;
  animation-delay: 3s;
}

.particle-5 {
  width: 14px;
  height: 14px;
  top: 45%;
  left: 15%;
  animation-delay: 4s;
}

/* 悬浮重置密码卡片容器 */
.forgot-card-container {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 2rem;
  padding-left: 18%;
}

/* 重置密码卡片 */
.forgot-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: cardFloat 6s ease-in-out infinite;
}

/* 品牌区域 */
.brand-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1d1d1f;
}

.auth-switch {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-text {
  font-size: 0.875rem;
  color: #86868b;
}

.auth-link {
  font-size: 0.875rem;
  color: #ff9500;
  text-decoration: none;
  font-weight: 500;
}

.auth-link:hover {
  color: #e6850e;
}

/* 表单标题 */
.form-header {
  margin-bottom: 2rem;
}

.form-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
}

.form-subtitle {
  font-size: 1rem;
  color: #86868b;
  margin: 0;
  font-weight: 400;
  line-height: 1.4;
}

/* 表单样式 */
.forgot-form {
  width: 100%;
}

.form-field {
  margin-bottom: 1.5rem;
}

.field-label {
  display: block;
  margin-bottom: 0.75rem;
  color: #1d1d1f;
  font-weight: 500;
  font-size: 0.875rem;
}

/* 输入框样式 */
.form-field :deep(.p-inputtext),
.form-field :deep(.p-password-input) {
  width: 100%;
  height: 52px;
  border: 2px solid #e5e5e7;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 1rem;
  background: #ffffff;
  transition: all 0.3s ease;
}

.form-field :deep(.p-inputtext:enabled:hover),
.form-field :deep(.p-password-input:enabled:hover) {
  border-color: #ff9500;
}

.form-field :deep(.p-inputtext:enabled:focus),
.form-field :deep(.p-password-input:enabled:focus) {
  border-color: #ff9500;
  box-shadow: 0 0 0 4px rgba(255, 149, 0, 0.1);
  outline: none;
}

.form-field :deep(.p-inputtext:disabled) {
  background-color: #f2f2f7;
  color: #86868b;
  cursor: not-allowed;
  border-color: #e5e5e7;
}

.form-field :deep(.p-invalid) {
  border-color: #ff3b30;
}

.error-message {
  color: #ff3b30;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: block;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 0.75rem;
  height: 4px;
  border-radius: 2px;
  background-color: #f2f2f7;
  overflow: hidden;
}

.strength-indicator {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-indicator.weak {
  width: 33.33%;
  background-color: #ff3b30;
}

.strength-indicator.medium {
  width: 66.66%;
  background-color: #ff9500;
}

.strength-indicator.strong {
  width: 100%;
  background-color: #34c759;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 52px;
  background: #ff9500;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-top: 1rem;
}

.submit-btn:hover {
  background: #e6850e;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 149, 0, 0.3);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled {
  background: #d2d2d7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 动画效果 */
@keyframes securityFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(3deg);
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) translateX(8px);
    opacity: 1;
  }
}

@keyframes securityGrow {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.3);
  }
}

@keyframes cardFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forgot-card-container {
    justify-content: center;
    padding: 1.5rem;
  }

  .forgot-card {
    max-width: 100%;
    padding: 2rem;
  }

  .form-title {
    font-size: 2rem;
  }

  .brand-section {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .forgot-card-container {
    padding: 1rem;
  }

  .forgot-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .form-title {
    font-size: 1.75rem;
  }
}
</style>
