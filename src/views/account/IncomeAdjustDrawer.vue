<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import type {
  IncomeAdjustItem,
  IncomeAdjustAccountRequest,
} from "../../types/adjustDetail";
import { incomeAdjustAccount } from "../../services/adjustDetail";
import { optionLoaders } from "../../utils/options";

const props = defineProps<{
  visible: boolean;
  incomeAdjustItem: IncomeAdjustItem | null;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}>();

const toast = useToast();
const drawerVisible = ref(false);
const loading = ref(false);
const submitted = ref(false);

// 表单验证错误状态
const fieldErrors = ref<Record<string, string>>({});

// 表单数据
const formData = ref<IncomeAdjustAccountRequest>({
  order_no: "",
  sub_order_no: "",
  charge_month: 0,
  adjust_month: 0,
  adjust_amount: 0,
  adjust_tax: 0,
  adjust_reason_class: "",
  adjust_reason: "",
});

// 日期选择器的值
const chargeMonthDate = ref<Date | null>(null);
const adjustMonthDate = ref<Date | null>(null);

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    drawerVisible.value = newVal;
    if (newVal && props.incomeAdjustItem) {
      initFormData();
    }
  }
);

// 监听内部状态变化
watch(drawerVisible, (newVal) => {
  emit("update:visible", newVal);
  if (!newVal) {
    resetForm();
  }
});

// 初始化表单数据
const initFormData = () => {
  if (props.incomeAdjustItem) {
    const item = props.incomeAdjustItem;

    formData.value = {
      order_no: item.order_num,
      sub_order_no: item.total_num,
      charge_month: 0,
      adjust_month: 0,
      adjust_amount: 0,
      adjust_tax: item.tax_rate || 0,
      adjust_reason_class: "",
      adjust_reason: "",
    };
  }
};

// 重置表单
const resetForm = () => {
  formData.value = {
    order_no: "",
    sub_order_no: "",
    charge_month: 0,
    adjust_month: 0,
    adjust_amount: 0,
    adjust_tax: 0,
    adjust_reason_class: "",
    adjust_reason: "",
  };
  chargeMonthDate.value = null;
  adjustMonthDate.value = null;
  submitted.value = false;
  fieldErrors.value = {};
};

// 清空表单错误
const clearFieldErrors = () => {
  fieldErrors.value = {};
};

// 监听权责月份日期选择器变化
watch(chargeMonthDate, (newDate) => {
  if (newDate) {
    const year = newDate.getFullYear();
    const month = String(newDate.getMonth() + 1).padStart(2, "0");
    formData.value.charge_month = parseInt(`${year}${month}`);
  }
});

// 监听调账月份日期选择器变化
watch(adjustMonthDate, (newDate) => {
  if (newDate) {
    const year = newDate.getFullYear();
    const month = String(newDate.getMonth() + 1).padStart(2, "0");
    formData.value.adjust_month = parseInt(`${year}${month}`);
  }
});

// 错误处理回调函数
const handleOptionsError = (_error: any, message: string) => {
  toast.add({
    severity: "error",
    summary: "错误",
    detail: message,
    life: 3000,
  });
};

// 加载调账原因分类选项
const adjustReasonClassOptions = ref<{ label: string; value: string }[]>([]);
const loadAdjustReasonClassOptions = async () =>
  optionLoaders.adjustReasonClass(adjustReasonClassOptions, handleOptionsError);

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
};

// 表单验证函数
const validateForm = (): boolean => {
  fieldErrors.value = {};
  let isValid = true;

  // 必填字段验证
  const requiredFields = [
    { key: "charge_month", label: "账期" },
    { key: "adjust_month", label: "调账月份" },
    { key: "adjust_amount", label: "调账金额" },
    { key: "adjust_reason_class", label: "调账原因分类" },
  ];

  requiredFields.forEach((field) => {
    const value = formData.value[field.key as keyof IncomeAdjustAccountRequest];
    if (
      value === null ||
      value === undefined ||
      (typeof value === "string" && value.trim() === "") ||
      (typeof value === "number" && (isNaN(value) || value === 0))
    ) {
      fieldErrors.value[field.key] = `${field.label}不能为空`;
      isValid = false;
    }
  });

  return isValid;
};

// 提交表单
const submitForm = async () => {
  submitted.value = true;

  // 前端表单验证
  if (!validateForm()) {
    toast.add({
      severity: "error",
      summary: "表单验证失败",
      detail: "请检查必填字段",
      life: 3000,
    });
    return;
  }

  try {
    loading.value = true;

    await incomeAdjustAccount(formData.value);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "无权责调账操作成功",
      life: 3000,
    });

    emit("success");
    drawerVisible.value = false;
    clearFieldErrors();
  } catch (error: any) {
    // 处理422验证错误
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: error.response?.data?.message || "无权责调账操作失败",
        life: 3000,
      });
    }
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadAdjustReasonClassOptions();
});
</script>

<template>
  <Drawer
    v-model:visible="drawerVisible"
    position="right"
    :style="{ width: '60rem' }"
    :modal="true"
    :closable="true"
    :dismissable="false"
    :showCloseIcon="true"
    :header="`无权责调账 - ${incomeAdjustItem?.order_num || ''}`"
    class="adjust-drawer p-fluid"
  >
    <div v-if="incomeAdjustItem" class="p-4">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="order_no">订单编号</label>
                <InputText
                  v-model="formData.order_no"
                  disabled
                  class="w-full"
                />
              </div>
              <div class="field">
                <label for="sub_order_no">子订单编号</label>
                <InputText
                  v-model="formData.sub_order_no"
                  disabled
                  class="w-full"
                />
              </div>
              <div class="field">
                <label for="adjust_tax">调账税率</label>
                <InputNumber
                  v-model="formData.adjust_tax"
                  disabled
                  suffix="%"
                  :maxFractionDigits="2"
                  class="w-full"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 调账信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">调账信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <!-- 权责月份 -->
              <div class="field">
                <label for="charge_month" class="required">账期</label>
                <DatePicker
                  v-model="chargeMonthDate"
                  view="month"
                  dateFormat="yymm"
                  showIcon
                  class="w-full"
                  :class="{ 'p-invalid': fieldErrors.charge_month }"
                />
                <small v-if="fieldErrors.charge_month" class="p-error">
                  {{ fieldErrors.charge_month }}
                </small>
              </div>
              <!-- 调账月份 -->
              <div class="field">
                <label for="adjust_month" class="required">调账月份</label>
                <DatePicker
                  v-model="adjustMonthDate"
                  view="month"
                  dateFormat="yymm"
                  showIcon
                  class="w-full"
                  :class="{ 'p-invalid': fieldErrors.adjust_month }"
                />
                <small v-if="fieldErrors.adjust_month" class="p-error">
                  {{ fieldErrors.adjust_month }}
                </small>
              </div>
              <!-- 调账金额 -->
              <div class="field">
                <label for="adjust_amount" class="required">调账金额</label>
                <InputNumber
                  v-model="formData.adjust_amount"
                  :maxFractionDigits="2"
                  :step="0.1"
                  class="w-full"
                  :class="{ 'p-invalid': fieldErrors.adjust_amount }"
                  showButtons
                />
                <small v-if="fieldErrors.adjust_amount" class="p-error">
                  {{ fieldErrors.adjust_amount }}
                </small>
              </div>
              <!-- 调账分类 -->
              <div class="field">
                <label for="adjust_reason_class" class="required"
                  >调账原因分类</label
                >
                <Select
                  v-model="formData.adjust_reason_class"
                  :options="adjustReasonClassOptions"
                  optionLabel="label"
                  optionValue="value"
                  class="w-full"
                  :class="{ 'p-invalid': fieldErrors.adjust_reason_class }"
                />
                <small v-if="fieldErrors.adjust_reason_class" class="p-error">
                  {{ fieldErrors.adjust_reason_class }}
                </small>
              </div>
            </div>
            <!-- 调账原因 -->
            <div class="field mt-4">
              <label for="adjust_reason">调账原因</label>
              <Textarea
                v-model="formData.adjust_reason"
                rows="3"
                class="w-full"
                :class="{ 'p-invalid': fieldErrors.adjust_reason }"
              />
            </div>
          </Fluid>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="closeDrawer"
        />
        <Button
          label="确认调账"
          icon="pi pi-check"
          @click="submitForm"
          severity="success"
          :loading="loading"
          :disabled="loading"
        />
      </div>
    </template>
  </Drawer>
</template>

<style scoped>
.form-section {
  margin-bottom: 2rem;
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  background: var(--p-surface-card);
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--p-text-color);
  margin: 0 0 1rem 0;
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* 抽屉样式 */
:deep(.adjust-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

/* 表单验证错误样式 */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 0.2rem rgba(255, 59, 48, 0.2) !important;
}

/* 按钮样式 */
:deep(.p-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.p-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
