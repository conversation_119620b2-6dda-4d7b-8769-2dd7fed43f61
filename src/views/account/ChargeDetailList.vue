<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  getChargeDetails,
  exportChargeDetails,
} from "../../services/chargeDetail";
import type {
  ChargeDetailItem,
  ChargeDetailParams,
} from "../../types/chargeDetail";
import { useToast } from "primevue/usetoast";
import ChargeDetailDialog from "./ChargeDetailDialog.vue";
import { isoFormatYYmm, formatCurrency, formatChargeMonth } from "../../utils/common";

const chargeDetails = ref<ChargeDetailItem[]>();
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();
const exporting = ref(false);

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选参数
const filterOrderNo = ref("");
const filterChargeMonth = ref<Date | null>(null);
const filterAdjustMonth = ref<Date | null>(null);

// 详情弹框相关
const chargeDetailVisible = ref(false);
const viewingChargeDetail = ref<ChargeDetailItem | null>(null);

// 加载收入权责列表数据
const loadChargeDetails = async () => {
  try {
    loading.value = true;
    const params: ChargeDetailParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (filterOrderNo.value) {
      params.order_no = filterOrderNo.value;
    }
    if (filterChargeMonth.value) {
      params.charge_month = isoFormatYYmm(filterChargeMonth.value);
    }
    if (filterAdjustMonth.value) {
      params.adjust_month = isoFormatYYmm(filterAdjustMonth.value);
    }

    // 过滤空值
    Object.keys(params).forEach((key) => {
      if (params[key] === "" || params[key] === null) {
        delete params[key];
      }
    });

    const response = await getChargeDetails(params);
    chargeDetails.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载收入权责列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadChargeDetails();
};

// 搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadChargeDetails();
};

// 重置筛选
const resetFilters = () => {
  filterOrderNo.value = "";
  filterChargeMonth.value = null;
  filterAdjustMonth.value = null;
  lazyParams.value.page = 1;
  loadChargeDetails();
};

// 查看详情
const viewChargeDetail = (chargeDetail: ChargeDetailItem) => {
  viewingChargeDetail.value = chargeDetail;
  chargeDetailVisible.value = true;
};

// 导出收入权责列表
const handleExport = async () => {
  try {
    exporting.value = true;

    // 构建导出参数，包含当前的筛选条件
    const exportParams: ChargeDetailParams = {};

    if (filterOrderNo.value) {
      exportParams.order_no = filterOrderNo.value;
    }
    if (filterChargeMonth.value) {
      exportParams.charge_month = isoFormatYYmm(filterChargeMonth.value);
    }
    if (filterAdjustMonth.value) {
      exportParams.adjust_month = isoFormatYYmm(filterAdjustMonth.value);
    }

    // 调用导出方法
    const response = await exportChargeDetails(exportParams);

    // 创建下载链接
    const url = window.URL.createObjectURL(response.blob);
    const link = document.createElement("a");
    link.href = url;

    // 从响应中获取文件名，如果没有则使用默认值
    link.download = response.filename || "收入权责明细.xlsx";

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "文件导出成功",
      life: 3000,
    });
  } catch (error) {
    console.error("Export failed:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "文件导出失败",
      life: 3000,
    });
  } finally {
    exporting.value = false;
  }
};

onMounted(() => {
  loadChargeDetails();
});
</script>

<template>
  <div class="charge-detail-list-container">
    <div class="card">
      <!-- 筛选工具栏 -->
      <Toolbar class="mb-2">
        <template #end>
          <div class="filter-section">
            <FloatLabel class="mr-2">
              <InputText v-model="filterOrderNo" />
              <label>订单编号</label>
            </FloatLabel>
            <FloatLabel class="mr-2">
              <DatePicker
                v-model="filterChargeMonth"
                view="month"
                dateFormat="yymm"
                showIcon
              />
              <label>账期</label>
            </FloatLabel>
            <FloatLabel class="mr-2">
              <DatePicker
                v-model="filterAdjustMonth"
                view="month"
                dateFormat="yymm"
                showIcon
              />
              <label>调整账期</label>
            </FloatLabel>
          </div>
          <Button
            label="搜索"
            icon="pi pi-search"
            @click="handleSearch"
            class="mr-2 button-primary"
          />
          <Button
            label="重置"
            icon="pi pi-refresh"
            severity="secondary"
            outlined
            @click="resetFilters"
            class="button-secondary"
          />
          <Divider layout="vertical" />
          <Button
            icon="pi pi-file-export"
            @click="handleExport"
            severity="help"
            class="button-primary"
            :loading="exporting"
            :disabled="exporting"
          />
        </template>
      </Toolbar>

      <!-- 数据表格 -->
      <DataTable
        :value="chargeDetails"
        :lazy="true"
        :paginator="true"
        :rows="20"
        :rowsPerPageOptions="[10, 20, 50]"
        :totalRecords="totalRecords"
        :loading="loading"
        @page="onPage($event)"
        showGridlines
        stripedRows
        scrollable
        scrollHeight="calc(100vh - 22rem)"
      >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无收入权责数据</p>
          </div>
        </template>

        <Column
          field="sign_contract_entity"
          header="主体"
          style="min-width: 15rem"
        />
        <Column field="order_no" header="订单号" style="min-width: 15rem" />
        <Column field="sub_order_no" header="子订单" style="min-width: 15rem" />
        <Column
          field="customer_name"
          header="客户名称"
          style="min-width: 20rem"
        />
        <Column field="charge_month" header="权责账期" style="min-width: 10rem">
          <template #body="slotProps">
            {{ formatChargeMonth(slotProps.data.charge_month) }}
          </template>
        </Column>
        <Column field="adjust_month" header="调整账期" style="min-width: 10rem">
          <template #body="slotProps">
            {{ formatChargeMonth(slotProps.data.adjust_month) }}
          </template>
        </Column>
        <Column field="fee_amount" header="权责金额" style="min-width: 12rem">
          <template #body="slotProps">
            {{ formatCurrency(slotProps.data.fee_amount, slotProps.data.currency_type) }}
          </template>
        </Column>
        <Column
          field="income_type"
          header="收入分类"
          style="min-width: 10rem"
        />
        <Column field="pay_type" header="付费方式" style="min-width: 10rem" />
        <Column header="操作" style="min-width: 8rem" frozen alignFrozen="right">
          <template #body="slotProps">
            <Button
              icon="pi pi-eye"
              severity="info"
              outlined
              rounded
              @click="viewChargeDetail(slotProps.data)"
              v-tooltip.top="'查看详情'"
              class="action-button"
            />
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- 详情弹框 -->
    <ChargeDetailDialog
      v-model:visible="chargeDetailVisible"
      :chargeDetail="viewingChargeDetail"
      @update:visible="chargeDetailVisible = $event"
    />
  </div>
</template>

<style scoped>
/* Apple Design System - 收入权责列表页面样式 */
.charge-detail-list-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

/* 筛选工具栏样式 */
.filter-section {
  display: flex;
  align-items: end;
  flex-wrap: wrap;
}

.filter-section label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6e6e73;
  margin: 0;
}

.button-primary {
  border: none !important;
  transition: all 0.2s ease !important;
}

.button-primary:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3) !important;
}

.button-secondary {
  border: 1px solid #d1d1d6 !important;
  transition: all 0.2s ease !important;
}

.button-secondary:hover {
  background: #f5f5f7 !important;
  border-color: #a1a1aa !important;
  transform: translateY(-1px) !important;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f8f9fa !important;
  border-bottom: 2px solid #e9ecef !important;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  border-bottom: 1px solid #f1f1f1 !important;
  font-size: 0.9rem !important;
  color: #1d1d1f !important;
  padding: 0.5rem 1rem !important;
}

.action-button {
  transition: all 0.2s ease !important;
}

.action-button:hover {
  background: rgba(0, 122, 255, 0.1) !important;
  transform: scale(1.1) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charge-detail-list-container {
    padding: 0.5rem;
  }

  .filter-section {
    flex-direction: column;
    gap: 1rem;
  }

  .action-buttons {
    width: 100%;
    justify-content: stretch;
  }

  .action-buttons .button-primary,
  .action-buttons .button-secondary {
    flex: 1;
  }
}
</style>
