@import "tailwindcss";
@import "tailwindcss-primeui";
@import '@fortawesome/fontawesome-free/css/all.min.css';

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  font-size: 80%;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light;
  background-color: #f8f9fa;
  --surface-border: #dee2e6;
  --white: white;
  --primary-color: #3498db;
  --text-color-secondary: #6b7280;
  --green-500: green;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

#app {
  width: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
}

ul {
  line-height: 1.8;
}

/* 全局通用样式 - 空状态消息 */
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}
